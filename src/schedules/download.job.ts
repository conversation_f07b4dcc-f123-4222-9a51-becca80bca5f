import logger from '../services/logger.service';
import { TaskService } from '../services/task.service';
import { FoodTaskService } from '../services/foodTask.service';
import configs from '../configs'; // 匯入 configs
import { SMSService } from '../services/sms.service';
import { Utils } from '@clinico/clinico-node-framework';

export default class downloadJob {
    private company: string;
    constructor(company: string) {
        this.company = company;
    }
    async run(): Promise<void> {
        try {
            const taskService = new TaskService(this.company);
            await taskService.download();
        } catch (err) {
            logger.error(err.message);
        } finally {
            await this.sendCollectedSmsEmail();
        }
    }

    async repayRun(): Promise<void> {
        try {
            const taskService = new TaskService(this.company);
            await taskService.repayDownload();
        } catch (err) {
            logger.error(err.message);
        } finally {
            await this.sendCollectedSmsEmail();
        }
    }

    async reRun(shipDate: Date): Promise<void> {
        try {
            const taskService = new TaskService(this.company, shipDate);
            await taskService.download();
        } catch (err) {
            logger.error(err.message);
        } finally {
            await this.sendCollectedSmsEmail();
        }
    }

    async foodRun(): Promise<void> {
        try {
            const taskService = new FoodTaskService(this.company);
            await taskService.download();
        } catch (err) {
            logger.error(err.message);
        } finally {
            await this.sendCollectedSmsEmail();
        }
    }

    private async sendCollectedSmsEmail(): Promise<void> {
        // 篩選出當前公司的排程簡訊
        const currentCompanySmsMessages =
            SMSService.collectedSmsMessages.filter(
                (msg) => msg.company === this.company && msg.source === 'SCHEDULE',
            );

        if (currentCompanySmsMessages.length === 0) {
            logger.info(
                `沒有收集到 ${this.company} 公司的簡訊內容，無需發送稽核郵件。`,
            );
            return;
        }

        const emailSubject = `[稽核測試] 繳費失敗簡訊內容彙整通知 - ${
            this.company
        } - ${new Date().toLocaleDateString('zh-TW')}`;
        let emailBody = `
            <p>Hi Terry</p>
            <p>以下是本次排程中收集到的繳費失敗簡訊內容：</p>
            <table border="1" style="border-collapse: collapse; width: 100%;">
                <thead>
                    <tr>
                        <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">訂單編號</th>
                        <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">狀態</th>
                        <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">簡訊內容</th>
                        <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">備註</th>
                    </tr>
                </thead>
                <tbody>
        `;

        currentCompanySmsMessages.forEach((msg) => {
            const statusText = {
                'SUCCESS': '✅ 發送成功',
                'FAILED': '❌ 發送失敗',
                'NO_PHONE': '⚠️ 無手機號碼',
                'COLLECTED': '📝 已收集'
            }[msg.status] || msg.status;

            const errorNote = msg.errorMessage ? msg.errorMessage : '';

            emailBody += `
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;">${msg.applicationId}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${statusText}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${msg.smsContent}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${errorNote}</td>
                </tr>
            `;
        });

        emailBody += `
                </tbody>
            </table>
            <p>此郵件僅供稽核測試使用，簡訊並未實際發送。</p>
            <p>謝謝。</p>
        `;

        const toEmails =
            process.env.NODE_ENV === 'production'
                ? '<EMAIL>' // 實際的 Terry 的電子郵件
                : '<EMAIL>'; // 測試環境發送給自己

        try {
            await Utils.Mailer.send({
                to: [toEmails].toString(),
                cc: ['<EMAIL>'].toString(),
                subject:
                    (process.env.NODE_ENV === 'production' ? '' : '[TEST]') +
                    emailSubject,
                body: emailBody,
            });
            logger.info(`成功將彙整後的簡訊內容發送至稽核郵件。`);
        } catch (error) {
            logger.error(`發送彙整簡訊稽核郵件時發生錯誤: ${error.message}`);
        } finally {
            // 清空當前公司已收集的排程簡訊內容
            SMSService.collectedSmsMessages =
                SMSService.collectedSmsMessages.filter(
                    (msg) => !(msg.company === this.company && msg.source === 'SCHEDULE'),
                );
        }
    }
}

process.on('unhandledRejection', console.dir);
