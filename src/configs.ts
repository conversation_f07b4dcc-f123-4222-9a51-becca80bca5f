import * as path from 'path';

class Configs {
    esun = {
        jarPath: path.join(__dirname, '/../ESUN/FileExchang_NFS.jar'),
        uploadFolder: path.join(__dirname, '/../ESUN/upload'),
        downloadFolder: path.join(__dirname, '/../ESUN/download'),
        uploadBackUpFolder: path.join(__dirname, '/../ESUN/backup/upload'),
        downloadBackUpFolder: path.join(__dirname, '/../ESUN/backup/download'),
        clinico: {
            account: 'HEARING',
            password: process.env.HA_PASSWORD ?? '',
            machineId: '**********',
            shopId: 'F787',
        },
        skd: {
            account: 'WISELIFE',
            password: process.env.SKD_PASSWORD ?? '',
            machineId: '**********',
            shopId: 'F787',
        },
        ib: {
            account: 'BAYBAY',
            password: process.env.IB_PASSWORD ?? '',
            machineId: '**********',
            shopId: 'F787',
        },
    };
    bank = {
        exportACHFolder: path.join(__dirname, '/../ESUN/ACH/export'),
        backupACHFolder: path.join(__dirname, '/../ESUN/ACH/backup'),
        serviceMail: '',
        code: '8081115',
        clinico: {
            code: '8081115',
            account: '*************',
            identity: '********',
        },
        skd: {
            code: '8081115',
            account: '*************',
            identity: '********',
        },
        ib: {
            code: '8081115',
            account: '*************',
            identity: '********',
        },
    };
    notification = {
        slack: {
            webhookUrl:
                '*******************************************************************************',
        },
    };
    log = {
        path: path.join(__dirname, '/../logs'),
    };
    crawler = {
        screenshot: {
            path: __dirname + '/../vpos/screenshot',
        },
    };
    smtp = {
        url: 'http://micro-smtp-api.cloud.clinico.com.tw/api/send/erp',
        headers: {
            'license-source': 'VPOS',
            'license-token':
                'CW7X9TAPNHBJ6YK7SLHDBLB37TPZY6U2K95UQ2S9QMNTZF5X6HHH7TGCTKVG9BWL',
        },
    };
    redmine = {
        url: 'http://asking.clinico.com.tw/',
        apiKey: 'a6313e99d870fde8c408ec0e844fce97c5e108d3',
    };
    ldap = {
        url: 'https://ldap-api.micro.clinico.cloud/api/auth',
        headers: {
            'license-source': 'VPOS',
            'license-token':
                'E6621A8C93B841B12BE47EB77B6EA1179F4CC3677A495E8244F1E66ABE05D842',
        },
    };
    sms = {
        url: process.env.SMS_URL || '',
        headers: {
            'license-source': 'VPOS',
            'license-token':
                '803BD017C1C3B782801BB32C8B93519D315192B13B0EB9E3BD92074A6349A8FF',
        },
        // enableSending: process.env.SMS_ENABLE_SENDING === 'true', // 新增配置選項
        enableSending: true,
    };
}
const configs = new Configs();
export default configs;
